# uncomment the lines below if you need to install specific version of libraries if using this notebook 
# in an environment where these libraries are not installed 
#! mamba install pandas==1.3.3  -y
#! mamba install numpy=1.21.2 -y

# import pandas library
import pandas as pd
import numpy as np

from pyodide.http import pyfetch

async def download(url, filename):
    response = await pyfetch(url)
    if response.status == 200:
        with open(filename, "wb") as f:
            f.write(await response.bytes())

file_path='https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/IBMDeveloperSkillsNetwork-DA0101EN-SkillsNetwork/labs/Data%20files/auto.csv'

await download(file_path, "auto.csv")
file_name="auto.csv"

df = pd.read_csv(file_name)

#filepath = "https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/IBMDeveloperSkillsNetwork-DA0101EN-SkillsNetwork/labs/Data%20files/auto.csv"
#df = pd.read_csv(filepath, header=None)

# show the first 5 rows using dataframe.head() method
print("The first 5 rows of the dataframe") 
df.head(5)

import pandas as pd
df = pd.read_csv("auto.csv", header=None)
print("The last 10 rows of the dataframe\n")
df.tail(10)

# create headers list
headers = ["symboling","normalized-losses","make","fuel-type","aspiration", "num-of-doors","body-style",
         "drive-wheels","engine-location","wheel-base", "length","width","height","curb-weight","engine-type",
         "num-of-cylinders", "engine-size","fuel-system","bore","stroke","compression-ratio","horsepower",
         "peak-rpm","city-mpg","highway-mpg","price"]
print("headers\n", headers)

df.columns = headers
df.columns

df.head(10)

df1=df.replace('?',np.NaN)


df=df1.dropna(subset=["price"], axis=0)
df.head(20)

# Write your code below and press Shift+Enter to execute 


df.to_csv("automobile.csv", index=False)

df.dtypes


# check the data type of data frame "df" by .dtypes
print(df.dtypes)

dataframe.describe()

df.describe()

# describe all the columns in "df" 
df.describe(include = "all")

# Write your code below and press Shift+Enter to execute 


dataframe.info()

# look at the info of "df"
df.info()